# Tech Stack

## Backend
- **Runtime**: Node.js 20 with TypeScript
- **Module System**: ES modules with NodeNext resolution
- **Framework**: Serverless v4 with AWS Lambda
- **Database**: DynamoDB with TTL (Aurora PostgreSQL disabled)
- **API**: API Gateway with Lambda integration
- **Validation**: Zod schemas for input validation
- **HTTP Client**: Axios for external API calls
- **Date/Time**: moment-timezone

## Frontend
- **Framework**: React 19 (no React imports needed in JSX)
- **Build Tool**: Vite
- **Language**: TypeScript with strict mode
- **State Management**: Redux Toolkit with redux-persist
- **Routing**: React Router v7
- **Styling**: Tailwind CSS v4
- **Error Handling**: React Error Boundary
- **Notifications**: Sonner
- **Theme**: next-themes for dark mode support

## Infrastructure
- **AWS Services**: Lambda, DynamoDB, S3, CloudFront, VPC, SSM
- **IaC**: Serverless Framework v4
- **Profile**: AWS profile "payrix" required for all deployments