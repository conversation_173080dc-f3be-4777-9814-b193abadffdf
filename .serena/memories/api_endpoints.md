# API Endpoints

## Active Endpoints

### Merchant Operations
- `POST /merchants/onboard` - Complete merchant onboarding
  - Multi-owner support
  - MCC codes for business details
  - Bank account integration

### Payment Operations
- `POST /payments/generate-payment-config` - Generate payment configuration
- `POST /payments/generate-integration-token` - Create iframe tokens for secure payments
- `POST /payments/validate-iframe-token` - Validate payment tokens
- `GET/POST /payments/iframe-config` - Get iframe configuration (CORS: allow all origins)
- `GET/POST /payments/token-status` - Check token status

## Security Requirements
- **Token Format**: 64-character hex strings
- **Merchant ID**: Alphanumeric with underscore/hyphen allowed
- **Amount Range**: $0.50 - $100,000
- **Return URLs**: HTTPS required
- **CORS**: Payment iframe endpoints allow all origins, others use specific origins with credentials

## Validation
- All endpoints use Zod schemas for input validation
- Structured error responses
- Input validation middleware applied

## Adding New Endpoints
1. Create Lambda function in `functions/src/`
2. Add to `functions/serverless.yml`
3. Apply validation middleware and error handling
4. Test locally with `npm run offline`