# Auth-Clear Project Overview

Auth-Clear is a payment processing platform focused on merchant onboarding and payment integration using Payrix API.

## Purpose
- Complete merchant onboarding with multi-owner support
- Secure payment processing through iframe integration
- PCI-compliant payment token generation and validation
- Clean, modular architecture with ~70% code reduction from refactoring

## Architecture
Multi-stack serverless architecture:
1. **Infrastructure Stack** - VPC, DynamoDB PaymentTokens table, Secrets (AWS SSM)
2. **Frontend Stack** - S3 static hosting with CloudFront CDN
3. **Functions Stack** - Lambda functions with API Gateway
4. **Session Manager** - Database bastion (currently disabled)

## Current State (January 2025)
- Database layer disabled (TypeORM entities commented out)
- Authentication disabled (Lambda authorizer off)
- Focus on payment processing and merchant onboarding
- All files kept under 250 lines for better maintainability
- Uses iframe-based payment solutions for PCI compliance