# Task Completion Checklist

## Before Marking a Task Complete

1. **Code Quality**
   - Run `npm run lint` to check for linting errors
   - Ensure no files exceed 250 lines (refactor if needed)
   - Remove any unnecessary comments
   - Check for code duplication and use existing utilities

2. **Error Handling**
   - All async/await operations wrapped in try/catch
   - Proper error messages and structured error responses
   - Edge cases considered and handled

3. **Validation**
   - Use existing Zod schemas for input validation
   - Validate all user inputs
   - Check security requirements (token formats, URL validation)

4. **Testing**
   - For API changes: Test with `npm run offline`
   - For frontend changes: Test with `npm run start:frontend`
   - Test CORS headers for payment endpoints
   - Verify iframe integration on demo pages if payment-related

5. **Environment**
   - If infrastructure changed: Run `npm run generate-env`
   - Ensure AWS profile is set correctly (payrix)

6. **Documentation**
   - Update CLAUDE.md if adding new patterns or commands
   - Keep changes simple with minimal impact
   - Follow existing patterns in the codebase

## Post-Completion
- Add a review section summarizing changes
- Mark todo items as completed
- Consider if any follow-up tasks are needed