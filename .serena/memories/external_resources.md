# External Resources

## Payrix Documentation
- Main docs: `https://resource.payrix.com/docs`
- API docs: `https://portal.payrix.com/docs/api`
- Sample integration: `/Users/<USER>/Work/Auth-Clear/auth-clear-ui`

## Payment Integration Guidelines
- Use iframe-based solutions for PCI compliance
- Validate merchants via Payrix API before token generation
- Consolidate multiple payment endpoints into streamlined flows
- Maintain existing design patterns

## Key Integration Points
- Merchant validation
- Token generation and validation
- Iframe configuration
- Payment processing
- Multi-owner support for merchants
- MCC (Merchant Category Code) integration