# Code Style and Conventions

## General Rules (Updated)
- Act as a senior developer
- Refactor any file over 250 lines into smaller modules
- Don't add comments unless absolutely necessary
- Every change should be as simple as possible with minimal impact
- Always check codebase and reuse existing code if possible
- Use web search extensively for unknown libraries or complex problems
- Think step by step about edge cases
- Ask for clarification if unsure
- Use MCP tools when needed

## TypeScript Configuration
- ES modules with NodeNext resolution
- Strict mode enabled
- React 19 (no React imports needed in JSX files)
- ESLint flat config with typescript-eslint

## Code Organization
- Single Responsibility Principle (SRP) for all functions
- Modular design with clear separation of concerns
- Use existing validation patterns (Zod schemas)
- Handle all errors and edge cases properly
- Use try/catch for all async/await operations

## Design Principles
- Elegant minimalism with functional design
- Mobile and desktop responsive
- Clear information hierarchy
- Well-proportioned white space

## Security
- Input validation middleware on all endpoints
- Structured error handling
- Never expose or log secrets/keys
- HTTPS required for return URLs
- Token format: 64-character hex strings