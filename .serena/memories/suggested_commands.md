# Suggested Commands

## Development Commands
```bash
# Generate environment variables from AWS (required for local dev)
npm run generate-env

# Start frontend development server
npm run start:frontend

# Start serverless offline for API testing
npm run offline

# Run ESLint
npm run lint
```

## Deployment Commands (Deploy in order: infra → frontend → functions)
```bash
# Deploy all stacks in correct order
npm run deploy:all

# Deploy individual stacks
npm run deploy:infra      # Infrastructure stack
npm run deploy:frontend   # Frontend to S3/CloudFront
npm run deploy:functions  # Lambda functions

# Remove all stacks in reverse order
npm run remove:all
```

## Debugging Commands
```bash
# Tail specific function logs
npm run logs:create-merchant
npm run logs:list-merchants
```

## AWS Profile
**IMPORTANT**: Always use `--aws-profile payrix` for all AWS commands

## System Commands (Darwin/macOS)
- `git` - Version control
- `ls` - List directory contents
- `cd` - Change directory
- `grep` or `rg` (ripgrep) - Search file contents
- `find` - Find files and directories

## Frontend Specific
```bash
cd frontend
npm run dev      # Start dev server
npm run build    # Build for production
npm run lint     # Run ESLint
npm run deploy   # Deploy to S3/CloudFront
```

## Functions Specific
```bash
cd functions
npm run lint      # Run ESLint
npm run lint:fix  # Fix ESLint issues
```