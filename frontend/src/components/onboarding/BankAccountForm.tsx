import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { updateFormData, nextStep, prevStep } from "../../redux/slices/onboardingSlice.ts";
import { useState } from "react";
import { toast } from "sonner";

const getAccountMethods = (isSoleProprietor: boolean) =>
  isSoleProprietor
    ? [
        { value: 8, label: "Personal Checking Account" },
        { value: 9, label: "Personal Savings Account" },
        { value: 10, label: "Business Checking Account" },
        { value: 11, label: "Business Savings Account" },
      ]
    : [
        { value: 10, label: "Business Checking Account" },
        { value: 11, label: "Business Savings Account" },
        { value: 8, label: "Personal Checking Account" },
        { value: 9, label: "Personal Savings Account" },
      ];

const formatDigits = (value: string, maxLength: number) => value.replace(/\D/g, "").slice(0, maxLength);

const BankAccountForm = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [verificationMethod, setVerificationMethod] = useState<"manual" | "upload">("manual");
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadPreview, setUploadPreview] = useState<string | null>(null);
  const isSoleProprietor = formData.type === 1;
  const account = formData.accounts?.[0] || {
    primary: 1,
    account: {
      method: isSoleProprietor ? 8 : 10,
      number: "",
      routing: "",
    },
  };
  const accountMethods = getAccountMethods(isSoleProprietor);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    if (!account.account.routing?.trim()) newErrors.routing = "Routing number is required";
    if (!account.account.number?.trim()) newErrors.number = "Account number is required";
    if (account.account.routing && !/^\d{9}$/.test(account.account.routing.replace(/\D/g, ""))) {
      newErrors.routing = "Routing number must be 9 digits";
    }
    if (account.account.number && !/^\d{4,17}$/.test(account.account.number.replace(/\D/g, ""))) {
      newErrors.number = "Account number must be between 4-17 digits";
    }
    if (!termsAccepted) newErrors.terms = "You must acknowledge and agree to the terms and conditions";

    // Validate void check upload if that verification method is selected
    if (verificationMethod === "upload" && !uploadedFile) {
      newErrors.voidCheck = "Please upload a void check image";
    }

    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) {
      toast.error("Please fix the following errors:", {
        description: Object.values(newErrors).join(", "),
      });
      return false;
    }
    return true;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      dispatch(nextStep());
    }
  };

  const handleChange = (field: string, value: string | number) => {
    const updatedAccount = field.startsWith("account.")
      ? {
          ...account,
          currency: "USD",
          account: { ...account.account, [field.replace("account.", "")]: value },
        }
      : { ...account, currency: "USD", [field]: value };
    dispatch(updateFormData({ accounts: [updatedAccount] }));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Bank Account Information</h1>
            <p className="text-gray-600 mt-1">Enter your business bank account details for payment processing</p>
          </div>

          <form onSubmit={handleSubmit} className="px-8 py-8">
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Account Details</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="lg:col-span-2">
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">Account Type *</label>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                      Primary Account
                    </span>
                  </div>
                  <select
                    value={account.account.method || ""}
                    onChange={(e) => handleChange("account.method", parseInt(e.target.value))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  >
                    <option value="">Select account type</option>
                    {accountMethods.map((method) => (
                      <option key={method.value} value={method.value}>
                        {method.label}
                      </option>
                    ))}
                  </select>
                  <p className="text-gray-500 text-sm mt-1">
                    {isSoleProprietor
                      ? "Personal accounts are typically used for sole proprietorships, but corporate accounts are also available"
                      : "Corporate accounts are recommended for business entities"}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Routing Number *</label>
                  <input
                    type="text"
                    value={account.account.routing || ""}
                    onChange={(e) => handleChange("account.routing", formatDigits(e.target.value, 9))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.routing ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="*********"
                    maxLength={9}
                  />
                  {errors.routing && (
                    <div className="flex items-start mt-1">
                      <svg className="w-4 h-4 text-red-600 mr-1 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-red-600 text-sm">{errors.routing}</p>
                    </div>
                  )}
                  <p className="text-gray-500 text-sm mt-1">9-digit number found on the bottom of your checks</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Account Number *</label>
                  <input
                    type="text"
                    value={account.account.number || ""}
                    onChange={(e) => handleChange("account.number", formatDigits(e.target.value, 17))}
                    className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                      errors.number ? "border-red-300 bg-red-50" : "border-gray-300"
                    }`}
                    placeholder="*********0123456"
                    maxLength={17}
                  />
                  {errors.number && (
                    <div className="flex items-start mt-1">
                      <svg className="w-4 h-4 text-red-600 mr-1 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-red-600 text-sm">{errors.number}</p>
                    </div>
                  )}
                  <p className="text-gray-500 text-sm mt-1">Account number found on your checks or bank statements</p>
                </div>
              </div>
            </div>

            {/* Bank Verification Options */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Verification Method</h2>
              <div className="space-y-4">
                <label className="relative flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="verificationMethod"
                    value="manual"
                    checked={verificationMethod === "manual"}
                    onChange={() => setVerificationMethod("manual")}
                    className="mt-1 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="ml-3">
                    <span className="block text-sm font-medium text-gray-900">Manual Entry</span>
                    <span className="block text-sm text-gray-500">Enter your bank account details manually</span>
                  </div>
                </label>

                <label className="relative flex items-start p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                  <input
                    type="radio"
                    name="verificationMethod"
                    value="upload"
                    checked={verificationMethod === "upload"}
                    onChange={() => setVerificationMethod("upload")}
                    className="mt-1 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <div className="ml-3">
                    <span className="block text-sm font-medium text-gray-900">Upload Void Check</span>
                    <span className="block text-sm text-gray-500">Upload an image of your voided check for automatic verification</span>
                  </div>
                </label>
              </div>

              {/* Void Check Upload Section */}
              {verificationMethod === "upload" && (
                <div className="mt-6">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    {!uploadedFile ? (
                      <div>
                        <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                          <path
                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                            strokeWidth={2}
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <div className="mt-4">
                          <label htmlFor="check-upload" className="cursor-pointer">
                            <span className="text-sm font-medium text-blue-600 hover:text-blue-500">Upload a voided check</span>
                            <input
                              id="check-upload"
                              name="check-upload"
                              type="file"
                              className="sr-only"
                              accept="image/*"
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) {
                                  setUploadedFile(file);
                                  // Clear void check error when file is uploaded
                                  if (errors.voidCheck) {
                                    setErrors({ ...errors, voidCheck: "" });
                                  }
                                  const reader = new FileReader();
                                  reader.onloadend = () => {
                                    setUploadPreview(reader.result as string);
                                  };
                                  reader.readAsDataURL(file);
                                }
                              }}
                            />
                          </label>
                          <p className="text-xs text-gray-500 mt-1">or drag and drop</p>
                        </div>
                        <p className="text-xs text-gray-500 mt-2">PNG, JPG, GIF up to 10MB</p>
                      </div>
                    ) : (
                      <div>
                        {uploadPreview && <img src={uploadPreview} alt="Uploaded check" className="mx-auto h-32 w-auto object-contain mb-4" />}
                        <p className="text-sm text-gray-900 font-medium">{uploadedFile.name}</p>
                        <p className="text-xs text-gray-500 mt-1">{(uploadedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                        <button
                          type="button"
                          onClick={() => {
                            setUploadedFile(null);
                            setUploadPreview(null);
                          }}
                          className="mt-4 text-sm font-medium text-red-600 hover:text-red-500"
                        >
                          Remove file
                        </button>
                      </div>
                    )}
                  </div>
                  {errors.voidCheck && (
                    <div className="flex items-start mt-2">
                      <svg className="w-4 h-4 text-red-600 mr-1 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-red-600 text-sm">{errors.voidCheck}</p>
                    </div>
                  )}
                  <p className="text-sm text-gray-500 mt-2">Please ensure the check is clearly voided and all account information is visible.</p>
                </div>
              )}
            </div>

            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Bank Account Verification</h2>
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-6 mb-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-amber-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-amber-900 mb-1">Bank Verification Required</h3>
                    <div className="text-sm text-amber-800 space-y-1">
                      <p>To complete your application, you will need to verify your bank account using one of these methods:</p>
                      <ul className="list-disc list-inside ml-4 mt-2">
                        <li>Instant verification through Plaid (recommended)</li>
                        <li>Upload a voided check</li>
                      </ul>
                      <p className="mt-2">Bank verification will be requested after initial application review.</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-blue-900 mb-1">Important Information</h3>
                    <div className="text-sm text-blue-800 space-y-1">
                      <p>• This account will be used for payment processing deposits</p>
                      <p>• Ensure the account is active and in good standing</p>
                      <p>• The account holder name should match your business name</p>
                      <p>• Business accounts are preferred for business transactions</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mb-10">
              <div className={`bg-gray-50 border rounded-lg p-6 ${errors.terms ? "border-red-300 bg-red-50" : "border-gray-200"}`}>
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="terms"
                    checked={termsAccepted}
                    onChange={(e) => setTermsAccepted(e.target.checked)}
                    className={`w-4 h-4 border rounded focus:ring-blue-500 mt-0.5 ${
                      errors.terms ? "border-red-300 text-red-600 focus:ring-red-500" : "border-gray-300 text-blue-600"
                    }`}
                  />
                  <label htmlFor="terms" className="text-sm text-gray-700">
                    <span className="font-medium">I acknowledge and agree</span> that the bank account information provided is accurate and that I
                    have authorization to use this account for payment processing. I understand that providing false information may result in account
                    suspension or termination.
                  </label>
                </div>
                {errors.terms && (
                  <div className="flex items-start mt-2 ml-7">
                    <svg className="w-4 h-4 text-red-600 mr-1 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-red-600 text-sm">{errors.terms}</p>
                  </div>
                )}
              </div>
            </div>

            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200"
              >
                Previous
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200"
              >
                Review & Submit
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BankAccountForm;
