import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { updateFormData, nextStep, prevStep } from "../../redux/slices/onboardingSlice.ts";
import { useState } from "react";
import { toast } from "sonner";
import { FinCenNotice, OwnershipSummary, OwnerFormSection, AccountCreationSection } from "./sections";
import { Member, DEFAULT_MEMBER, DEFAULT_ADDITIONAL_MEMBER } from "./constants/ownerConstants";
import { validateOwnerForm } from "./utils/ownerValidation";

const OwnerInfoFormMultiple = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get members array or create default
  const members = (formData.merchant?.members as Member[]) || [DEFAULT_MEMBER];

  const validateForm = () => {
    const validationErrors = validateOwnerForm(members, formData.username, formData.password, formData.confirmPassword);
    setErrors(validationErrors);
    return validationErrors;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length === 0) {
      dispatch(nextStep());
    } else {
      const errorCount = Object.keys(validationErrors).length;
      toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? "s" : ""} before continuing.`);

      // Scroll to first error
      setTimeout(() => {
        const firstErrorKey = Object.keys(validationErrors)[0];
        if (firstErrorKey) {
          const errorElement = document.querySelector(`[data-error-key="${firstErrorKey}"]`);
          if (errorElement) {
            errorElement.scrollIntoView({ behavior: "smooth", block: "center" });
          }
        }
      }, 100);
    }
  };

  const handleChange = (memberIndex: number, field: string, value: string | number) => {
    // Clear error for this field if it exists
    const errorKey = `member${memberIndex}.${field}`;
    if (errors[errorKey]) {
      const newErrors = { ...errors };
      delete newErrors[errorKey];
      setErrors(newErrors);
    }

    const updatedMembers = [...members];
    updatedMembers[memberIndex] = {
      ...updatedMembers[memberIndex],
      [field]: value,
    };

    dispatch(
      updateFormData({
        merchant: {
          dba: "",
          new: 1,
          mcc: "",
          status: "1",
          ...formData.merchant,
          members: updatedMembers,
        },
      })
    );
  };

  const addOwner = () => {
    dispatch(
      updateFormData({
        merchant: {
          dba: formData.merchant?.dba || "",
          new: 1,
          mcc: formData.merchant?.mcc || "",
          status: formData.merchant?.status || "1",
          ...formData.merchant,
          members: [...members, DEFAULT_ADDITIONAL_MEMBER],
        },
      })
    );
  };

  const handlePrimaryChange = (selectedIndex: number) => {
    const updatedMembers = members.map((m, i) => ({
      ...m,
      primary: i === selectedIndex ? "1" : "0",
    }));
    dispatch(
      updateFormData({
        merchant: {
          dba: formData.merchant?.dba || "",
          new: 1,
          mcc: formData.merchant?.mcc || "",
          status: formData.merchant?.status || "1",
          ...formData.merchant,
          members: updatedMembers,
        },
      })
    );
  };

  const removeOwner = (index: number) => {
    if (members.length > 1) {
      const updatedMembers = members.filter((_, i) => i !== index);
      dispatch(
        updateFormData({
          merchant: {
            dba: formData.merchant?.dba || "",
            new: 1,
            mcc: formData.merchant?.mcc || "",
            status: formData.merchant?.status || "1",
            ...formData.merchant,
            members: updatedMembers,
          },
        })
      );
    }
  };

  const handleAccountFieldChange = (field: string, value: string | boolean) => {
    dispatch(updateFormData({ [field]: value, createAccount: true }));

    // Real-time validation for username and password fields
    if (typeof value === "string") {
      const newErrors = { ...errors };

      if (field === "username") {
        if (value) {
          if (!/^[a-z0-9]{3,50}$/.test(value)) {
            newErrors.username = "Username must be 3-50 characters and contain only lowercase letters and numbers";
          } else if (!/(?=.*\d)/.test(value)) {
            newErrors.username = "Username must include at least one number";
          } else {
            delete newErrors.username;
          }
        }
      }

      if (field === "password") {
        if (value) {
          const passwordErrors = [];
          if (value.length < 8) passwordErrors.push("at least 8 characters");
          if (!/(?=.*[a-z])/.test(value)) passwordErrors.push("one lowercase letter");
          if (!/(?=.*[A-Z])/.test(value)) passwordErrors.push("one uppercase letter");
          if (!/(?=.*\d)/.test(value)) passwordErrors.push("one number");
          if (!/(?=.*[@$!%*?&])/.test(value)) passwordErrors.push("one special character (@$!%*?&)");

          if (passwordErrors.length > 0) {
            newErrors.password = `Password must include: ${passwordErrors.join(", ")}`;
          } else {
            delete newErrors.password;
          }
        }

        if (formData.confirmPassword && value !== formData.confirmPassword) {
          newErrors.confirmPassword = "Passwords do not match";
        } else if (formData.confirmPassword && value === formData.confirmPassword) {
          delete newErrors.confirmPassword;
        }
      }

      if (field === "confirmPassword") {
        if (value && formData.password && value !== formData.password) {
          newErrors.confirmPassword = "Passwords do not match";
        } else {
          delete newErrors.confirmPassword;
        }
      }

      setErrors(newErrors);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header */}
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Owner Information</h1>
            <p className="text-gray-600 mt-1">Details about business owners and principals</p>
          </div>

          {/* FINCEN Language */}
          <div className="px-8 pt-6">
            <FinCenNotice />
          </div>

          <form onSubmit={handleSubmit} className="px-8 pb-8">
            {/* Error Summary */}
            {Object.keys(errors).length > 0 && (
              <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-red-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <h3 className="text-sm font-medium text-red-900 mb-2">Please fix the following errors:</h3>
                    <ul className="list-disc list-inside text-sm text-red-700 space-y-1">
                      {Object.entries(errors).map(([key, message]) => (
                        <li key={key}>{message}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}
            {/* Total Ownership Display */}
            <OwnershipSummary members={members} />

            {/* Multiple Owners Question */}
            <div className="mb-8">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h3 className="text-sm font-medium text-gray-900 mb-4">Does any other principal own 25% or more of the business?</h3>
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={addOwner}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    Yes, Add Additional Principal
                  </button>
                  {members.length === 1 && <span className="text-sm text-gray-600 py-2">Currently showing 1 principal</span>}
                  {members.length > 1 && <span className="text-sm text-gray-600 py-2">Currently showing {members.length} principals</span>}
                </div>
              </div>
            </div>

            {/* Owner Forms */}
            {members.map((member, index) => (
              <OwnerFormSection
                key={index}
                member={member}
                index={index}
                canRemove={members.length > 1}
                onFieldChange={handleChange}
                onRemove={removeOwner}
                onPrimaryChange={handlePrimaryChange}
                errors={errors}
              />
            ))}

            {/* Account Creation */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Auth Clear Portal Account</h2>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <p className="text-gray-700 text-sm">
                  A Auth Clear portal account will be created for this merchant, allowing access to their dashboard to view transactions, manage
                  settings, and access reports.
                </p>
              </div>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                    <input
                      type="text"
                      value={formData.username || ""}
                      onChange={(e) => handleAccountFieldChange("username", e.target.value.toLowerCase())}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.username ? "border-red-300 bg-red-50" : "border-gray-300"
                      }`}
                      placeholder="merchant123"
                    />
                    {errors.username && <p className="text-red-600 text-sm mt-1">{errors.username}</p>}
                    {!errors.username && (
                      <p className="text-gray-500 text-xs mt-1">3-50 characters, lowercase letters and at least one number only</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                    <input
                      type="password"
                      value={formData.password || ""}
                      onChange={(e) => handleAccountFieldChange("password", e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.password ? "border-red-300 bg-red-50" : "border-gray-300"
                      }`}
                      placeholder="Create a strong password"
                    />
                    {errors.password && <p className="text-red-600 text-sm mt-1">{errors.password}</p>}
                    {!errors.password && (
                      <p className="text-gray-500 text-xs mt-1">Min 8 chars: uppercase, lowercase, number, special character (@$!%*?&)</p>
                    )}
                  </div>

                  <div className="lg:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Confirm Password *</label>
                    <input
                      type="password"
                      value={formData.confirmPassword || ""}
                      onChange={(e) => handleAccountFieldChange("confirmPassword", e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.confirmPassword ? "border-red-300 bg-red-50" : "border-gray-300"
                      }`}
                      placeholder="Confirm your password"
                    />
                    {errors.confirmPassword && <p className="text-red-600 text-sm mt-1">{errors.confirmPassword}</p>}
                  </div>
                </div>

                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <svg className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <div className="text-sm text-amber-800">
                      <p className="font-medium mb-1">Account Creation Notice</p>
                      <p>
                        The account will be created after successful merchant onboarding. You will receive login credentials and a link to access your
                        Auth Clear portal dashboard.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200"
              >
                Previous
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200"
              >
                Continue to Bank Account
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default OwnerInfoFormMultiple;
