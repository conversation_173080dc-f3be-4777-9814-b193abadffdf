import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { updateFormData, nextStep, prevStep } from "../../redux/slices/onboardingSlice.ts";
import { useState } from "react";
import { toast } from "sonner";

const OwnerInfoFormMultiple = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Get members array or create default
  const members = formData.merchant?.members || [
    {
      title: "",
      first: "",
      middle: "",
      last: "",
      ssn: "",
      dob: "",
      dl: "",
      dlstate: "",
      ownership: 10000, // 100% in basis points for single owner
      significantResponsibility: 1,
      politicallyExposed: 0,
      email: "",
      phone: "",
      primary: "1",
      address1: "",
      address2: "",
      city: "",
      state: "",
      zip: "",
      country: "USA",
    },
  ];

  // Calculate total ownership
  const totalOwnership = members.reduce((sum, m) => sum + (m.ownership || 0), 0);

  const states = [
    "AL",
    "AK",
    "AZ",
    "AR",
    "CA",
    "CO",
    "CT",
    "DE",
    "FL",
    "GA",
    "HI",
    "ID",
    "IL",
    "IN",
    "IA",
    "KS",
    "KY",
    "LA",
    "ME",
    "MD",
    "MA",
    "MI",
    "MN",
    "MS",
    "MO",
    "MT",
    "NE",
    "NV",
    "NH",
    "NJ",
    "NM",
    "NY",
    "NC",
    "ND",
    "OH",
    "OK",
    "OR",
    "PA",
    "RI",
    "SC",
    "SD",
    "TN",
    "TX",
    "UT",
    "VT",
    "VA",
    "WA",
    "WV",
    "WI",
    "WY",
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Check total ownership
    if (totalOwnership !== 10000) {
      newErrors.totalOwnership = `Total ownership must equal 100% (currently ${(totalOwnership / 100).toFixed(2)}%)`;
    }

    // Validate each member with 25% or more ownership
    members.forEach((member, index) => {
      if (member.ownership >= 2500 || members.length === 1) {
        // Required field validations
        if (!member.first?.trim()) newErrors[`member${index}.first`] = "First name is required";
        if (!member.last?.trim()) newErrors[`member${index}.last`] = "Last name is required";
        if (!member.title?.trim()) newErrors[`member${index}.title`] = "Business title is required";
        if (!member.email?.trim()) newErrors[`member${index}.email`] = "Email is required";
        if (!member.phone?.trim()) newErrors[`member${index}.phone`] = "Phone is required";
        if (!member.address1?.trim()) {
          newErrors[`member${index}.address1`] = "Address is required";
        } else if (/^(p\.?\s?o\.?\s?box|post\s?office\s?box)/i.test(member.address1)) {
          newErrors[`member${index}.address1`] = "PO Boxes are not acceptable for personal address";
        }
        if (!member.city?.trim()) newErrors[`member${index}.city`] = "City is required";
        if (!member.state?.trim()) newErrors[`member${index}.state`] = "State is required";
        if (!member.zip?.trim()) newErrors[`member${index}.zip`] = "ZIP code is required";
        if (!member.dob?.trim()) newErrors[`member${index}.dob`] = "Date of birth is required";
        if (!member.ssn?.trim()) newErrors[`member${index}.ssn`] = "SSN is required";
        if (!member.dl?.trim()) newErrors[`member${index}.dl`] = "Driver&apos;s license number is required";
        if (!member.dlstate?.trim()) newErrors[`member${index}.dlstate`] = "Driver&apos;s license state is required";

        // Format validations
        if (member.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(member.email)) {
          newErrors[`member${index}.email`] = "Please enter a valid email address";
        }

        if (member.phone && !/^\d{10}$/.test(member.phone.replace(/\D/g, ""))) {
          newErrors[`member${index}.phone`] = "Phone must be 10 digits";
        }

        if (member.ssn && !/^\d{9}$/.test(member.ssn.replace(/\D/g, ""))) {
          newErrors[`member${index}.ssn`] = "SSN must be 9 digits";
        }

        if (member.zip && !/^\d{5}(-\d{4})?$/.test(member.zip)) {
          newErrors[`member${index}.zip`] = "ZIP code must be 5 digits or 5-4 format";
        }

        if (member.dlstate && !/^[A-Z]{2}$/.test(member.dlstate)) {
          newErrors[`member${index}.dlstate`] = "Driver&apos;s license state must be a valid 2-letter state code";
        }

        // Date of birth validation (must be at least 18 years old)
        if (member.dob) {
          const dobDate = new Date(member.dob);
          const today = new Date();
          const age = today.getFullYear() - dobDate.getFullYear();
          const monthDiff = today.getMonth() - dobDate.getMonth();

          if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dobDate.getDate())) {
            if (age - 1 < 18) {
              newErrors[`member${index}.dob`] = "Owner must be at least 18 years old";
            }
          } else if (age < 18) {
            newErrors[`member${index}.dob`] = "Owner must be at least 18 years old";
          }
        }
      }
    });

    // Validate primary contact designation
    const primaryCount = members.filter((m) => m.primary === "1").length;
    if (primaryCount === 0) {
      newErrors.primaryContact = "You must designate one owner as the primary contact";
    } else if (primaryCount > 1) {
      newErrors.primaryContact = "Only one owner can be designated as the primary contact";
    }

    // Account creation validation (mandatory)
    if (!formData.username?.trim()) newErrors.username = "Username is required";
    if (!formData.password?.trim()) newErrors.password = "Password is required";
    if (formData.password && formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    // Username validation
    if (formData.username) {
      if (!/^[a-z0-9]{3,50}$/.test(formData.username)) {
        newErrors.username = "Username must be 3-50 characters and contain only lowercase letters and numbers";
      } else if (!/(?=.*\d)/.test(formData.username)) {
        newErrors.username = "Username must include at least one number";
      }
    }

    // Password validation
    if (formData.password) {
      const passwordErrors = [];
      if (formData.password.length < 8) passwordErrors.push("at least 8 characters");
      if (!/(?=.*[a-z])/.test(formData.password)) passwordErrors.push("one lowercase letter");
      if (!/(?=.*[A-Z])/.test(formData.password)) passwordErrors.push("one uppercase letter");
      if (!/(?=.*\d)/.test(formData.password)) passwordErrors.push("one number");
      if (!/(?=.*[@$!%*?&])/.test(formData.password)) passwordErrors.push("one special character (@$!%*?&)");

      if (passwordErrors.length > 0) {
        newErrors.password = `Password must include: ${passwordErrors.join(", ")}`;
      }
    }

    setErrors(newErrors);
    return newErrors; // Return errors object instead of boolean
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length === 0) {
      dispatch(nextStep());
    } else {
      const errorCount = Object.keys(validationErrors).length;
      toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? "s" : ""} before continuing.`);

      // Scroll to first error
      setTimeout(() => {
        const firstErrorKey = Object.keys(validationErrors)[0];
        if (firstErrorKey) {
          const errorElement = document.querySelector(`[data-error-key="${firstErrorKey}"]`);
          if (errorElement) {
            errorElement.scrollIntoView({ behavior: "smooth", block: "center" });
          }
        }
      }, 100);
    }
  };

  const handleChange = (memberIndex: number, field: string, value: string | number) => {
    // Clear error for this field if it exists
    const errorKey = `member${memberIndex}.${field}`;
    if (errors[errorKey]) {
      const newErrors = { ...errors };
      delete newErrors[errorKey];
      setErrors(newErrors);
    }

    const updatedMembers = [...members];
    updatedMembers[memberIndex] = {
      ...updatedMembers[memberIndex],
      [field]: value,
    };

    dispatch(
      updateFormData({
        merchant: {
          dba: "",
          new: 1,
          mcc: "",
          status: "1",
          ...formData.merchant,
          members: updatedMembers,
        },
      })
    );
  };

  const addOwner = () => {
    const newMember = {
      title: "",
      first: "",
      middle: "",
      last: "",
      ssn: "",
      dob: "",
      dl: "",
      dlstate: "",
      ownership: 0,
      significantResponsibility: 0,
      politicallyExposed: 0,
      email: "",
      phone: "",
      primary: "0",
      address1: "",
      address2: "",
      city: "",
      state: "",
      zip: "",
      country: "USA",
    };

    dispatch(
      updateFormData({
        merchant: {
          dba: formData.merchant?.dba || "",
          new: 1,
          mcc: formData.merchant?.mcc || "",
          status: formData.merchant?.status || "1",
          ...formData.merchant,
          members: [...members, newMember],
        },
      })
    );
  };

  const removeOwner = (index: number) => {
    if (members.length > 1) {
      const updatedMembers = members.filter((_, i) => i !== index);
      dispatch(
        updateFormData({
          merchant: {
            dba: formData.merchant?.dba || "",
            new: 1,
            mcc: formData.merchant?.mcc || "",
            status: formData.merchant?.status || "1",
            ...formData.merchant,
            members: updatedMembers,
          },
        })
      );
    }
  };

  const handleAccountFieldChange = (field: string, value: string | boolean) => {
    dispatch(updateFormData({ [field]: value, createAccount: true }));

    // Real-time validation for username and password fields
    if (typeof value === "string") {
      const newErrors = { ...errors };

      if (field === "username") {
        if (value) {
          if (!/^[a-z0-9]{3,50}$/.test(value)) {
            newErrors.username = "Username must be 3-50 characters and contain only lowercase letters and numbers";
          } else if (!/(?=.*\d)/.test(value)) {
            newErrors.username = "Username must include at least one number";
          } else {
            delete newErrors.username;
          }
        }
      }

      if (field === "password") {
        if (value) {
          const passwordErrors = [];
          if (value.length < 8) passwordErrors.push("at least 8 characters");
          if (!/(?=.*[a-z])/.test(value)) passwordErrors.push("one lowercase letter");
          if (!/(?=.*[A-Z])/.test(value)) passwordErrors.push("one uppercase letter");
          if (!/(?=.*\d)/.test(value)) passwordErrors.push("one number");
          if (!/(?=.*[@$!%*?&])/.test(value)) passwordErrors.push("one special character (@$!%*?&)");

          if (passwordErrors.length > 0) {
            newErrors.password = `Password must include: ${passwordErrors.join(", ")}`;
          } else {
            delete newErrors.password;
          }
        }

        if (formData.confirmPassword && value !== formData.confirmPassword) {
          newErrors.confirmPassword = "Passwords do not match";
        } else if (formData.confirmPassword && value === formData.confirmPassword) {
          delete newErrors.confirmPassword;
        }
      }

      if (field === "confirmPassword") {
        if (value && formData.password && value !== formData.password) {
          newErrors.confirmPassword = "Passwords do not match";
        } else {
          delete newErrors.confirmPassword;
        }
      }

      setErrors(newErrors);
    }
  };

  const formatSSN = (value: string) => {
    const digits = value.replace(/\D/g, "");
    if (digits.length <= 3) return digits;
    if (digits.length <= 5) return `${digits.slice(0, 3)}-${digits.slice(3)}`;
    return `${digits.slice(0, 3)}-${digits.slice(3, 5)}-${digits.slice(5, 9)}`;
  };

  const formatPhone = (value: string) => {
    const digits = value.replace(/\D/g, "");
    if (digits.length <= 3) return digits;
    if (digits.length <= 6) return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
    return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header */}
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Owner Information</h1>
            <p className="text-gray-600 mt-1">Details about business owners and principals</p>
          </div>

          {/* FINCEN Language */}
          <div className="px-8 pt-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <h3 className="text-sm font-medium text-blue-900 mb-2">Important Notice - FinCEN Requirements</h3>
              <p className="text-sm text-blue-800">
                To help the government fight the funding of terrorism and money laundering activities, federal law requires all financial institutions
                to obtain, verify, and record information that identifies each individual or business who opens an account.
              </p>
              <p className="text-sm text-blue-800 mt-2">
                <strong>What this means for you:</strong> When you open an account, we will ask for your identifiable information including your full
                name, address, date of birth, and other business information that will allow us to identify you. We may also ask to see your
                Identification Card, Driver&apos;s License, and/or other identifying documents.
              </p>
              <p className="text-sm text-blue-800 mt-2">
                <strong>Please Note:</strong> The following information must be provided for Sole Proprietors or each individual, if any, who directly
                or indirectly owns twenty-five percent (25%) or more of the ownership interest of the Legal Entity in this application as well as an
                individual with significant responsibility. A Legal Entity includes a general partnership, a corporation, limited liability company or
                other entity that is formed by a filing of a public document with a Secretary of State or similar office, and any similar business
                entity formed in the United States.
              </p>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="px-8 pb-8">
            {/* Error Summary */}
            {Object.keys(errors).length > 0 && (
              <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start">
                  <svg className="w-5 h-5 text-red-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div>
                    <h3 className="text-sm font-medium text-red-900 mb-2">Please fix the following errors:</h3>
                    <ul className="list-disc list-inside text-sm text-red-700 space-y-1">
                      {Object.entries(errors).map(([key, message]) => (
                        <li key={key}>{message}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}
            {/* Total Ownership Display */}
            {members.length > 1 && (
              <div
                className={`mb-6 p-4 rounded-lg ${
                  totalOwnership === 10000 ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className={`text-sm font-medium ${totalOwnership === 10000 ? "text-green-800" : "text-red-800"}`}>
                    Total Ownership: {(totalOwnership / 100).toFixed(2)}%
                  </span>
                  {totalOwnership !== 10000 && <span className="text-sm text-red-600">Must equal 100%</span>}
                </div>
              </div>
            )}

            {/* Multiple Owners Question */}
            <div className="mb-8">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h3 className="text-sm font-medium text-gray-900 mb-4">Does any other principal own 25% or more of the business?</h3>
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={addOwner}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
                  >
                    Yes, Add Additional Principal
                  </button>
                  {members.length === 1 && <span className="text-sm text-gray-600 py-2">Currently showing 1 principal</span>}
                  {members.length > 1 && <span className="text-sm text-gray-600 py-2">Currently showing {members.length} principals</span>}
                </div>
              </div>
            </div>

            {/* Owner Forms */}
            {members.map((member, index) => (
              <div key={index} className="mb-10 border border-gray-200 rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-medium text-gray-900">
                    Principal {index + 1} Information
                    {member.ownership > 0 && <span className="text-sm text-gray-600 ml-2">({(member.ownership / 100).toFixed(2)}% ownership)</span>}
                  </h2>
                  {members.length > 1 && (
                    <button type="button" onClick={() => removeOwner(index)} className="text-red-600 hover:text-red-800 text-sm font-medium">
                      Remove Principal
                    </button>
                  )}
                </div>

                {/* Primary Contact Designation */}
                <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="text-sm font-semibold text-blue-900 mb-2">Primary Contact Designation</h3>
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="primaryContact"
                      checked={member.primary === "1"}
                      onChange={() => {
                        const updatedMembers = members.map((m, i) => ({
                          ...m,
                          primary: i === index ? "1" : "0",
                        }));
                        dispatch(
                          updateFormData({
                            merchant: {
                              dba: formData.merchant?.dba || "",
                              new: 1,
                              mcc: formData.merchant?.mcc || "",
                              status: formData.merchant?.status || "1",
                              ...formData.merchant,
                              members: updatedMembers,
                            },
                          })
                        );
                      }}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="text-sm font-medium text-gray-700">
                      Select as primary contact for the business
                      {member.primary === "1" && <span className="text-blue-600 ml-2">(Currently Selected)</span>}
                    </span>
                  </label>
                  <p className="text-xs text-gray-600 mt-2">
                    The primary contact will receive all business communications and account notifications.
                  </p>
                  {errors.primaryContact && index === 0 && <p className="text-red-600 text-sm mt-1">{errors.primaryContact}</p>}
                </div>

                {/* Personal Information */}
                <div className="mb-8">
                  <h3 className="text-md font-medium text-gray-900 mb-4">Personal Information</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        First Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={member.first || ""}
                        onChange={(e) => handleChange(index, "first", e.target.value)}
                        data-error-key={`member${index}.first`}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.first`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="John"
                      />
                      {errors[`member${index}.first`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.first`]}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Middle Name</label>
                      <input
                        type="text"
                        value={member.middle || ""}
                        onChange={(e) => handleChange(index, "middle", e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        placeholder="Optional"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Last Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={member.last || ""}
                        onChange={(e) => handleChange(index, "last", e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.last`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="Smith"
                      />
                      {errors[`member${index}.last`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.last`]}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Business Title <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={member.title || ""}
                        onChange={(e) => handleChange(index, "title", e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.title`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="CEO, Owner, President, etc."
                      />
                      {errors[`member${index}.title`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.title`]}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Date of Birth *</label>
                      <input
                        type="date"
                        value={
                          member.dob
                            ? member.dob.length === 8
                              ? `${member.dob.slice(0, 4)}-${member.dob.slice(4, 6)}-${member.dob.slice(6, 8)}`
                              : member.dob
                            : ""
                        }
                        onChange={(e) => handleChange(index, "dob", e.target.value.replace(/-/g, ""))}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.dob`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                      />
                      {errors[`member${index}.dob`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.dob`]}</p>}
                      <p className="text-gray-500 text-sm mt-1">Must be at least 18 years old</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Social Security Number *</label>
                      <input
                        type="text"
                        value={formatSSN(member.ssn || "")}
                        onChange={(e) => handleChange(index, "ssn", e.target.value.replace(/\D/g, ""))}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.ssn`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="***********"
                        maxLength={11}
                      />
                      {errors[`member${index}.ssn`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.ssn`]}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Ownership Percentage *</label>
                      <div className="relative">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={Math.round((member.ownership || 0) / 100)}
                          onChange={(e) => handleChange(index, "ownership", parseInt(e.target.value) * 100 || 0)}
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-8 ${
                            errors[`member${index}.ownership`] ? "border-red-300 bg-red-50" : "border-gray-300"
                          }`}
                          placeholder="25"
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                          <span className="text-gray-500">%</span>
                        </div>
                      </div>
                      {member.ownership < 2500 && member.ownership > 0 && (
                        <p className="text-amber-600 text-sm mt-1">Only principals with 25% or more ownership are required</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="mb-8">
                  <h3 className="text-md font-medium text-gray-900 mb-4">Contact Information</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Personal Email *</label>
                      <input
                        type="email"
                        value={member.email || ""}
                        onChange={(e) => handleChange(index, "email", e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.email`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="<EMAIL>"
                      />
                      {errors[`member${index}.email`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.email`]}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Personal Phone *</label>
                      <input
                        type="tel"
                        value={formatPhone(member.phone || "")}
                        onChange={(e) => handleChange(index, "phone", e.target.value.replace(/\D/g, ""))}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.phone`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="(*************"
                        maxLength={14}
                      />
                      {errors[`member${index}.phone`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.phone`]}</p>}
                    </div>
                  </div>
                </div>

                {/* Personal Address */}
                <div className="mb-8">
                  <h3 className="text-md font-medium text-gray-900 mb-4">Personal Address</h3>
                  <p className="text-gray-600 mb-4">Enter home address (not business address)</p>
                  <p className="text-sm text-red-600 mb-4">* PO Boxes are not acceptable for onboarding</p>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="lg:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Street Address *</label>
                      <input
                        type="text"
                        value={member.address1 || ""}
                        onChange={(e) => handleChange(index, "address1", e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.address1`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="123 Home Street"
                      />
                      {errors[`member${index}.address1`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.address1`]}</p>}
                    </div>

                    <div className="lg:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Address Line 2</label>
                      <input
                        type="text"
                        value={member.address2 || ""}
                        onChange={(e) => handleChange(index, "address2", e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        placeholder="Apt, suite, etc. (optional)"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
                      <input
                        type="text"
                        value={member.city || ""}
                        onChange={(e) => handleChange(index, "city", e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.city`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="Los Angeles"
                      />
                      {errors[`member${index}.city`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.city`]}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
                      <select
                        value={member.state || ""}
                        onChange={(e) => handleChange(index, "state", e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.state`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                      >
                        <option value="">Select state</option>
                        {states.map((state) => (
                          <option key={state} value={state}>
                            {state}
                          </option>
                        ))}
                      </select>
                      {errors[`member${index}.state`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.state`]}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">ZIP Code *</label>
                      <input
                        type="text"
                        value={member.zip || ""}
                        onChange={(e) => handleChange(index, "zip", e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.zip`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="90210"
                      />
                      {errors[`member${index}.zip`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.zip`]}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Country</label>
                      <input type="text" value="USA" readOnly className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50" />
                    </div>
                  </div>
                </div>

                {/* Driver's License */}
                <div className="mb-8">
                  <h3 className="text-md font-medium text-gray-900 mb-4">Driver&apos;s License Information</h3>
                  <p className="text-gray-600 mb-4">Required for identity verification and KYC compliance</p>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Driver&apos;s License Number *</label>
                      <input
                        type="text"
                        value={member.dl || ""}
                        onChange={(e) => handleChange(index, "dl", e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.dl`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                        placeholder="********"
                      />
                      {errors[`member${index}.dl`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.dl`]}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Issuing State *</label>
                      <select
                        value={member.dlstate || ""}
                        onChange={(e) => handleChange(index, "dlstate", e.target.value)}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                          errors[`member${index}.dlstate`] ? "border-red-300 bg-red-50" : "border-gray-300"
                        }`}
                      >
                        <option value="">Select state</option>
                        {states.map((state) => (
                          <option key={state} value={state}>
                            {state}
                          </option>
                        ))}
                      </select>
                      {errors[`member${index}.dlstate`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.dlstate`]}</p>}
                    </div>
                  </div>
                </div>

                {/* Responsibilities & Status */}
                <div>
                  <h3 className="text-md font-medium text-gray-900 mb-4">Responsibilities & Status</h3>
                  <div className="space-y-4">
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={member.significantResponsibility === 1}
                          onChange={(e) => handleChange(index, "significantResponsibility", e.target.checked ? 1 : 0)}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label className="text-sm font-medium text-gray-700">This person has significant responsibility for the business</label>
                      </div>
                      <p className="text-gray-600 text-sm mt-2 ml-7">
                        Check this if the person is a CEO, CFO, Owner, VP, managing member, or similar controlling authority.
                      </p>
                    </div>

                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          checked={member.politicallyExposed === 1}
                          onChange={(e) => handleChange(index, "politicallyExposed", e.target.checked ? 1 : 0)}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                        />
                        <label className="text-sm font-medium text-gray-700">This person is politically exposed</label>
                      </div>
                      <p className="text-gray-600 text-sm mt-2 ml-7">
                        A politically exposed person is someone who, through their prominent position or influence, is more susceptible to being
                        involved in bribery or corruption.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Account Creation */}
            <div className="mb-10">
              <h2 className="text-lg font-medium text-gray-900 mb-6">Auth Clear Portal Account</h2>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <p className="text-gray-700 text-sm">
                  A Auth Clear portal account will be created for this merchant, allowing access to their dashboard to view transactions, manage
                  settings, and access reports.
                </p>
              </div>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                    <input
                      type="text"
                      value={formData.username || ""}
                      onChange={(e) => handleAccountFieldChange("username", e.target.value.toLowerCase())}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.username ? "border-red-300 bg-red-50" : "border-gray-300"
                      }`}
                      placeholder="merchant123"
                    />
                    {errors.username && <p className="text-red-600 text-sm mt-1">{errors.username}</p>}
                    {!errors.username && (
                      <p className="text-gray-500 text-xs mt-1">3-50 characters, lowercase letters and at least one number only</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                    <input
                      type="password"
                      value={formData.password || ""}
                      onChange={(e) => handleAccountFieldChange("password", e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.password ? "border-red-300 bg-red-50" : "border-gray-300"
                      }`}
                      placeholder="Create a strong password"
                    />
                    {errors.password && <p className="text-red-600 text-sm mt-1">{errors.password}</p>}
                    {!errors.password && (
                      <p className="text-gray-500 text-xs mt-1">Min 8 chars: uppercase, lowercase, number, special character (@$!%*?&)</p>
                    )}
                  </div>

                  <div className="lg:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Confirm Password *</label>
                    <input
                      type="password"
                      value={formData.confirmPassword || ""}
                      onChange={(e) => handleAccountFieldChange("confirmPassword", e.target.value)}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                        errors.confirmPassword ? "border-red-300 bg-red-50" : "border-gray-300"
                      }`}
                      placeholder="Confirm your password"
                    />
                    {errors.confirmPassword && <p className="text-red-600 text-sm mt-1">{errors.confirmPassword}</p>}
                  </div>
                </div>

                <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <svg className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <div className="text-sm text-amber-800">
                      <p className="font-medium mb-1">Account Creation Notice</p>
                      <p>
                        The account will be created after successful merchant onboarding. You will receive login credentials and a link to access your
                        Auth Clear portal dashboard.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation Buttons */}
            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200"
              >
                Previous
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200"
              >
                Continue to Bank Account
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default OwnerInfoFormMultiple;
